/**
 * Database Schema
 *
 * Drizzle ORM schema definitions for PostgreSQL database.
 * Includes tables for user management, authentication, and application data.
 */

import { relations } from 'drizzle-orm';
import { bigint, boolean, integer, pgTable, text, timestamp, uuid, varchar } from 'drizzle-orm/pg-core';

// ============================================================================
// User Management Tables
// ============================================================================

/**
 * Users table - Core user information
 */
export const users = pgTable('users', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  email: varchar('email', { length: 255 }).notNull().unique(),
  emailVerified: boolean('email_verified').default(false).notNull(),
  image: text('image'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

/**
 * Accounts table - OAuth and social provider accounts
 */
export const accounts = pgTable('accounts', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  accountId: varchar('account_id', { length: 255 }).notNull(),
  providerId: varchar('provider_id', { length: 255 }).notNull(),
  accessToken: text('access_token'),
  refreshToken: text('refresh_token'),
  idToken: text('id_token'),
  accessTokenExpiresAt: timestamp('access_token_expires_at'),
  refreshTokenExpiresAt: timestamp('refresh_token_expires_at'),
  scope: text('scope'),
  password: text('password'), // For email/password accounts
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

/**
 * Sessions table - User sessions
 */
export const sessions = pgTable('sessions', {
  id: varchar('id', { length: 255 }).primaryKey(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  expiresAt: timestamp('expires_at').notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

/**
 * Verification tokens table - Email verification, password reset, etc.
 */
export const verificationTokens = pgTable('verification_tokens', {
  id: uuid('id').primaryKey().defaultRandom(),
  identifier: varchar('identifier', { length: 255 }).notNull(),
  token: varchar('token', { length: 255 }).notNull().unique(),
  expires: timestamp('expires').notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

/**
 * Rate limit table - Better Auth rate limiting storage
 */
export const rateLimit = pgTable('rate_limit', {
  id: text('id').primaryKey().notNull(),
  key: varchar('key', { length: 255 }).notNull().unique(),
  count: integer('count').notNull().default(0),
  lastRequest: bigint('last_request', { mode: 'number' }).notNull(),
});

// ============================================================================
// Team Management Tables
// ============================================================================

/**
 * Teams table - Multi-tenant team management
 */
export const teams = pgTable('teams', {
  id: uuid('id').primaryKey().defaultRandom(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  description: text('description'),
  ownerId: uuid('owner_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  planType: varchar('plan_type', { length: 50 }).default('free').notNull(), // 'free' | 'pro' | 'enterprise'
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'), // Soft delete support
});

/**
 * Team members table - RBAC for team membership
 */
export const teamMembers = pgTable('team_members', {
  id: uuid('id').primaryKey().defaultRandom(),
  teamId: uuid('team_id')
    .notNull()
    .references(() => teams.id, { onDelete: 'cascade' }),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  role: varchar('role', { length: 50 }).default('member').notNull(), // 'owner' | 'admin' | 'manager' | 'member' | 'viewer'
  permissions: text('permissions'), // JSON array of specific permissions
  invitedBy: uuid('invited_by').references(() => users.id),
  joinedAt: timestamp('joined_at').defaultNow().notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'), // Soft delete support
});

// ============================================================================
// Billing Tables
// ============================================================================

/**
 * Billing subscriptions table - Team subscription management
 */
export const billingSubscriptions = pgTable('billing_subscriptions', {
  id: uuid('id').primaryKey().defaultRandom(),
  teamId: uuid('team_id')
    .notNull()
    .references(() => teams.id, { onDelete: 'cascade' }),
  provider: varchar('provider', { length: 50 }).notNull(), // 'paddle' | 'paypal'
  planId: varchar('plan_id', { length: 255 }).notNull(),
  status: varchar('status', { length: 50 }).notNull(), // 'active' | 'canceled' | 'past_due' | 'unpaid'
  currentPeriodStart: timestamp('current_period_start').notNull(),
  currentPeriodEnd: timestamp('current_period_end').notNull(),
  cancelAtPeriodEnd: boolean('cancel_at_period_end').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

/**
 * Billing transactions table - Payment transaction history
 */
export const billingTransactions = pgTable('billing_transactions', {
  id: uuid('id').primaryKey().defaultRandom(),
  teamId: uuid('team_id')
    .notNull()
    .references(() => teams.id, { onDelete: 'cascade' }),
  provider: varchar('provider', { length: 50 }).notNull(), // 'paddle' | 'paypal'
  transactionId: varchar('transaction_id', { length: 255 }).notNull().unique(),
  amount: integer('amount').notNull(), // Amount in cents
  currency: varchar('currency', { length: 3 }).default('USD').notNull(),
  status: varchar('status', { length: 50 }).notNull(), // 'completed' | 'pending' | 'failed' | 'refunded'
  type: varchar('type', { length: 50 }).default('subscription').notNull(), // 'subscription' | 'api_requests'
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

/**
 * Payment methods table - Stored payment methods for teams
 */
export const paymentMethods = pgTable('payment_methods', {
  id: uuid('id').primaryKey().defaultRandom(),
  teamId: uuid('team_id')
    .notNull()
    .references(() => teams.id, { onDelete: 'cascade' }),
  provider: varchar('provider', { length: 50 }).notNull(), // 'paddle' | 'paypal'
  providerPaymentMethodId: varchar('provider_payment_method_id', { length: 255 }).notNull(), // External payment method ID
  type: varchar('type', { length: 50 }).notNull(), // 'card' | 'paypal' | 'bank_transfer' | 'apple_pay' | 'google_pay'

  // Card details (for card payments)
  cardLast4: varchar('card_last4', { length: 4 }),
  cardBrand: varchar('card_brand', { length: 20 }), // 'visa' | 'mastercard' | 'amex' | etc.
  cardExpiryMonth: integer('card_expiry_month'),
  cardExpiryYear: integer('card_expiry_year'),

  // PayPal details (for PayPal payments)
  paypalEmail: varchar('paypal_email', { length: 255 }),

  // General details
  isDefault: boolean('is_default').default(false).notNull(),
  isActive: boolean('is_active').default(true).notNull(),

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

/**
 * Additional API requests table - Track purchased additional API request packages
 */
export const additionalApiRequests = pgTable('additional_api_requests', {
  id: uuid('id').primaryKey().defaultRandom(),
  teamId: uuid('team_id')
    .notNull()
    .references(() => teams.id, { onDelete: 'cascade' }),
  transactionId: uuid('transaction_id')
    .notNull()
    .references(() => billingTransactions.id, { onDelete: 'cascade' }),
  requestCount: integer('request_count').notNull(), // Number of additional requests purchased
  usedCount: integer('used_count').default(0).notNull(), // Number of requests used
  expiresAt: timestamp('expires_at').notNull(), // Expiration date (30 days from purchase)
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// ============================================================================
// Application Data Tables
// ============================================================================

/**
 * Projects table - User projects/collections (updated for multi-tenancy)
 */
export const projects = pgTable('projects', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'cascade' }), // Optional team association
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  isPublic: boolean('is_public').default(false).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'), // Soft delete support
});

/**
 * Collections table - Document collections within projects (updated for multi-tenancy)
 */
export const collections = pgTable('collections', {
  id: uuid('id').primaryKey().defaultRandom(),
  projectId: uuid('project_id')
    .notNull()
    .references(() => projects.id, { onDelete: 'cascade' }),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'cascade' }), // Optional team association
  name: varchar('name', { length: 255 }).notNull(),
  description: text('description'),
  category: varchar('category', { length: 50 }).default('docs').notNull(), // 'docs' | 'code'
  embeddingProvider: varchar('embedding_provider', { length: 50 }).default('cloudflare').notNull(),
  embeddingModel: varchar('embedding_model', { length: 255 })
    .default('@cf/baai/bge-large-en-v1.5')
    .notNull(),
  vectorDimensions: integer('vector_dimensions').default(1024).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'), // Soft delete support
});

/**
 * Documents table - Uploaded documents and their metadata (updated for soft delete)
 */
export const documents = pgTable('documents', {
  id: uuid('id').primaryKey().defaultRandom(),
  collectionId: uuid('collection_id')
    .notNull()
    .references(() => collections.id, { onDelete: 'cascade' }),
  filename: varchar('filename', { length: 255 }).notNull(),
  originalName: varchar('original_name', { length: 255 }).notNull(),
  mimeType: varchar('mime_type', { length: 100 }).notNull(),
  fileSize: integer('file_size').notNull(),
  contentHash: varchar('content_hash', { length: 64 }).notNull(), // SHA-256 hash
  r2Key: varchar('r2_key', { length: 500 }).notNull(), // R2 storage key
  processingStatus: varchar('processing_status', { length: 50 }).default('pending').notNull(),
  errorMessage: text('error_message'),
  chunkCount: integer('chunk_count').default(0).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'), // Soft delete support
});

/**
 * Document chunks table - Text chunks from processed documents
 */
export const documentChunks = pgTable('document_chunks', {
  id: uuid('id').primaryKey().defaultRandom(),
  documentId: uuid('document_id')
    .notNull()
    .references(() => documents.id, { onDelete: 'cascade' }),
  vectorId: varchar('vector_id', { length: 255 }).notNull(), // Vectorize vector ID
  chunkIndex: integer('chunk_index').notNull(),
  content: text('content').notNull(),
  contentHash: varchar('content_hash', { length: 64 }).notNull(),
  tokenCount: integer('token_count'),
  metadata: text('metadata'), // JSON metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

// ============================================================================
// System Tables
// ============================================================================

/**
 * API Keys table - Enhanced API keys with team support and usage tracking
 */
export const apiKeys = pgTable('api_keys', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'cascade' }), // Optional team association
  name: varchar('name', { length: 255 }).notNull(),
  keyHash: varchar('key_hash', { length: 255 }).notNull(), // Hashed API key
  keyPrefix: varchar('key_prefix', { length: 20 }).notNull(), // First few chars for identification
  permissions: text('permissions'), // JSON permissions
  scopedPermissions: text('scoped_permissions'), // JSON scoped permissions for team context
  usageLimit: integer('usage_limit'), // Monthly usage limit
  usageCount: integer('usage_count').default(0).notNull(), // Current usage count
  lastUsedAt: timestamp('last_used_at'),
  expiresAt: timestamp('expires_at'),
  isActive: boolean('is_active').default(true).notNull(),
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  deletedAt: timestamp('deleted_at'), // Soft delete support
});

/**
 * Audit logs table - System audit trail with team context
 */
export const auditLogs = pgTable('audit_logs', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id').references(() => users.id, { onDelete: 'set null' }),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'set null' }), // Optional team context
  action: varchar('action', { length: 100 }).notNull(),
  resource: varchar('resource', { length: 100 }).notNull(),
  resourceId: varchar('resource_id', { length: 255 }),
  details: text('details'), // JSON details
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
  createdAt: timestamp('created_at').defaultNow().notNull(),
});

/**
 * Email notification preferences table - User/team notification settings
 */
export const emailNotificationPreferences = pgTable('email_notification_preferences', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'cascade' }), // Optional team-specific preferences

  // Expiration notifications
  expirationWarnings: boolean('expiration_warnings').default(true).notNull(),
  expirationWarningDays: text('expiration_warning_days').default('[7,1]').notNull(), // JSON array of days

  // Rotation notifications
  rotationNotifications: boolean('rotation_notifications').default(true).notNull(),
  rotationFailureAlerts: boolean('rotation_failure_alerts').default(true).notNull(),

  // Bulk operation notifications
  bulkOperationSummaries: boolean('bulk_operation_summaries').default(true).notNull(),

  // Team-level notifications (for admins)
  teamExpirationSummaries: boolean('team_expiration_summaries').default(false).notNull(),

  // Delivery preferences
  emailEnabled: boolean('email_enabled').default(true).notNull(),
  webhookEnabled: boolean('webhook_enabled').default(false).notNull(),
  webhookUrl: varchar('webhook_url', { length: 500 }),

  // Frequency controls
  maxEmailsPerDay: integer('max_emails_per_day').default(10).notNull(),
  quietHoursStart: varchar('quiet_hours_start', { length: 5 }), // HH:MM format
  quietHoursEnd: varchar('quiet_hours_end', { length: 5 }), // HH:MM format
  quietHoursTimezone: varchar('quiet_hours_timezone', { length: 50 }).default('UTC'),

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

/**
 * Notification queue table - Queued email notifications
 */
export const notificationQueue = pgTable('notification_queue', {
  id: uuid('id').primaryKey().defaultRandom(),
  type: varchar('type', { length: 50 }).notNull(), // TEmailTemplate
  priority: varchar('priority', { length: 10 }).default('normal').notNull(), // low, normal, high, urgent

  // Recipient information
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  userEmail: varchar('user_email', { length: 255 }).notNull(),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'cascade' }),

  // Template context (JSON)
  context: text('context').notNull(), // IApiKeyEmailContext as JSON

  // Scheduling
  scheduledAt: timestamp('scheduled_at').defaultNow().notNull(),
  maxRetries: integer('max_retries').default(3).notNull(),
  retryCount: integer('retry_count').default(0).notNull(),

  // Status tracking
  status: varchar('status', { length: 20 }).default('pending').notNull(), // pending, processing, sent, failed, cancelled
  lastAttemptAt: timestamp('last_attempt_at'),
  sentAt: timestamp('sent_at'),
  error: text('error'),

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

/**
 * API key rotation history table - Track rotation events
 */
export const apiKeyRotationHistory = pgTable('api_key_rotation_history', {
  id: uuid('id').primaryKey().defaultRandom(),
  oldKeyId: uuid('old_key_id').notNull(), // Reference to the rotated key
  newKeyId: uuid('new_key_id')
    .notNull()
    .references(() => apiKeys.id, { onDelete: 'cascade' }),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'cascade' }),

  // Rotation details
  rotationType: varchar('rotation_type', { length: 20 }).default('manual').notNull(), // manual, automatic, scheduled
  reason: varchar('reason', { length: 100 }), // expiration, security, scheduled, etc.

  // Key information at time of rotation
  oldKeyName: varchar('old_key_name', { length: 255 }).notNull(),
  oldKeyPrefix: varchar('old_key_prefix', { length: 20 }).notNull(),
  newKeyPrefix: varchar('new_key_prefix', { length: 20 }).notNull(),

  // Metadata
  rotatedAt: timestamp('rotated_at').defaultNow().notNull(),
  rotatedBy: uuid('rotated_by').references(() => users.id, { onDelete: 'set null' }), // Who initiated the rotation
  ipAddress: varchar('ip_address', { length: 45 }),
  userAgent: text('user_agent'),
});

/**
 * API key rotation policies table - Automatic rotation policies
 */
export const apiKeyRotationPolicies = pgTable('api_key_rotation_policies', {
  id: uuid('id').primaryKey().defaultRandom(),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'cascade' }),

  // Policy settings
  enabled: boolean('enabled').default(true).notNull(),
  rotationIntervalDays: integer('rotation_interval_days').notNull(), // e.g., 90 days
  rotateBeforeExpirationDays: integer('rotate_before_expiration_days'), // e.g., 7 days before expiration

  // Overlap settings for seamless transition
  overlapPeriodHours: integer('overlap_period_hours').default(24).notNull(), // e.g., 24 hours overlap

  // Notification settings
  notifyBeforeRotation: boolean('notify_before_rotation').default(true).notNull(),
  notificationDays: integer('notification_days').default(3).notNull(), // e.g., 3 days before rotation

  // Filters - which keys to apply this policy to
  keyNamePattern: varchar('key_name_pattern', { length: 255 }), // regex pattern for key names
  permissions: text('permissions'), // JSON array of permissions

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
  lastAppliedAt: timestamp('last_applied_at'),
});

/**
 * Scheduled API key rotations table - Track scheduled rotation events
 */
export const scheduledApiKeyRotations = pgTable('scheduled_api_key_rotations', {
  id: uuid('id').primaryKey().defaultRandom(),
  keyId: uuid('key_id')
    .notNull()
    .references(() => apiKeys.id, { onDelete: 'cascade' }),
  userId: uuid('user_id')
    .notNull()
    .references(() => users.id, { onDelete: 'cascade' }),
  teamId: uuid('team_id').references(() => teams.id, { onDelete: 'cascade' }),

  // Scheduling details
  scheduledAt: timestamp('scheduled_at').notNull(),
  rotationType: varchar('rotation_type', { length: 20 }).default('scheduled').notNull(),
  reason: varchar('reason', { length: 100 }),
  overlapPeriodHours: integer('overlap_period_hours').default(24).notNull(),

  // Status tracking
  status: varchar('status', { length: 20 }).default('pending').notNull(), // pending, completed, cancelled, failed
  executedAt: timestamp('executed_at'),
  failureReason: text('failure_reason'),

  // Metadata
  createdAt: timestamp('created_at').defaultNow().notNull(),
  updatedAt: timestamp('updated_at').defaultNow().notNull(),
});

// ============================================================================
// Relations
// ============================================================================

export const usersRelations = relations(users, ({ many }) => ({
  accounts: many(accounts),
  sessions: many(sessions),
  projects: many(projects),
  apiKeys: many(apiKeys),
  auditLogs: many(auditLogs),
  ownedTeams: many(teams),
  teamMemberships: many(teamMembers),
  emailNotificationPreferences: many(emailNotificationPreferences),
  notificationQueue: many(notificationQueue),
  apiKeyRotationHistory: many(apiKeyRotationHistory),
  scheduledApiKeyRotations: many(scheduledApiKeyRotations),
}));

export const accountsRelations = relations(accounts, ({ one }) => ({
  user: one(users, {
    fields: [accounts.userId],
    references: [users.id],
  }),
}));

export const sessionsRelations = relations(sessions, ({ one }) => ({
  user: one(users, {
    fields: [sessions.userId],
    references: [users.id],
  }),
}));

// Team relations
export const teamsRelations = relations(teams, ({ one, many }) => ({
  owner: one(users, {
    fields: [teams.ownerId],
    references: [users.id],
  }),
  members: many(teamMembers),
  projects: many(projects),
  collections: many(collections),
  apiKeys: many(apiKeys),
  billingSubscriptions: many(billingSubscriptions),
  billingTransactions: many(billingTransactions),
  additionalApiRequests: many(additionalApiRequests),
  auditLogs: many(auditLogs),
}));

export const teamMembersRelations = relations(teamMembers, ({ one }) => ({
  team: one(teams, {
    fields: [teamMembers.teamId],
    references: [teams.id],
  }),
  user: one(users, {
    fields: [teamMembers.userId],
    references: [users.id],
  }),
  invitedByUser: one(users, {
    fields: [teamMembers.invitedBy],
    references: [users.id],
  }),
}));

// Billing relations
export const billingSubscriptionsRelations = relations(billingSubscriptions, ({ one }) => ({
  team: one(teams, {
    fields: [billingSubscriptions.teamId],
    references: [teams.id],
  }),
}));

export const billingTransactionsRelations = relations(billingTransactions, ({ one, many }) => ({
  team: one(teams, {
    fields: [billingTransactions.teamId],
    references: [teams.id],
  }),
  additionalApiRequests: many(additionalApiRequests),
}));

export const paymentMethodsRelations = relations(paymentMethods, ({ one }) => ({
  team: one(teams, {
    fields: [paymentMethods.teamId],
    references: [teams.id],
  }),
}));

export const additionalApiRequestsRelations = relations(additionalApiRequests, ({ one }) => ({
  team: one(teams, {
    fields: [additionalApiRequests.teamId],
    references: [teams.id],
  }),
  transaction: one(billingTransactions, {
    fields: [additionalApiRequests.transactionId],
    references: [billingTransactions.id],
  }),
}));

export const projectsRelations = relations(projects, ({ one, many }) => ({
  user: one(users, {
    fields: [projects.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [projects.teamId],
    references: [teams.id],
  }),
  collections: many(collections),
}));

export const collectionsRelations = relations(collections, ({ one, many }) => ({
  project: one(projects, {
    fields: [collections.projectId],
    references: [projects.id],
  }),
  team: one(teams, {
    fields: [collections.teamId],
    references: [teams.id],
  }),
  documents: many(documents),
}));

export const documentsRelations = relations(documents, ({ one, many }) => ({
  collection: one(collections, {
    fields: [documents.collectionId],
    references: [collections.id],
  }),
  chunks: many(documentChunks),
}));

export const documentChunksRelations = relations(documentChunks, ({ one }) => ({
  document: one(documents, {
    fields: [documentChunks.documentId],
    references: [documents.id],
  }),
}));

export const apiKeysRelations = relations(apiKeys, ({ one }) => ({
  user: one(users, {
    fields: [apiKeys.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [apiKeys.teamId],
    references: [teams.id],
  }),
}));

export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
  user: one(users, {
    fields: [auditLogs.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [auditLogs.teamId],
    references: [teams.id],
  }),
}));

// Email notification relations
export const emailNotificationPreferencesRelations = relations(
  emailNotificationPreferences,
  ({ one }) => ({
    user: one(users, {
      fields: [emailNotificationPreferences.userId],
      references: [users.id],
    }),
    team: one(teams, {
      fields: [emailNotificationPreferences.teamId],
      references: [teams.id],
    }),
  })
);

export const notificationQueueRelations = relations(notificationQueue, ({ one }) => ({
  user: one(users, {
    fields: [notificationQueue.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [notificationQueue.teamId],
    references: [teams.id],
  }),
}));

export const apiKeyRotationPoliciesRelations = relations(apiKeyRotationPolicies, ({ one }) => ({
  user: one(users, {
    fields: [apiKeyRotationPolicies.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [apiKeyRotationPolicies.teamId],
    references: [teams.id],
  }),
}));

export const apiKeyRotationHistoryRelations = relations(apiKeyRotationHistory, ({ one }) => ({
  newKey: one(apiKeys, {
    fields: [apiKeyRotationHistory.newKeyId],
    references: [apiKeys.id],
  }),
  user: one(users, {
    fields: [apiKeyRotationHistory.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [apiKeyRotationHistory.teamId],
    references: [teams.id],
  }),
  rotatedBy: one(users, {
    fields: [apiKeyRotationHistory.rotatedBy],
    references: [users.id],
  }),
}));

export const scheduledApiKeyRotationsRelations = relations(scheduledApiKeyRotations, ({ one }) => ({
  key: one(apiKeys, {
    fields: [scheduledApiKeyRotations.keyId],
    references: [apiKeys.id],
  }),
  user: one(users, {
    fields: [scheduledApiKeyRotations.userId],
    references: [users.id],
  }),
  team: one(teams, {
    fields: [scheduledApiKeyRotations.teamId],
    references: [teams.id],
  }),
}));

// ============================================================================
// Type Exports
// ============================================================================

export type TUser = typeof users.$inferSelect;
export type TNewUser = typeof users.$inferInsert;

export type TAccount = typeof accounts.$inferSelect;
export type TNewAccount = typeof accounts.$inferInsert;

export type TSession = typeof sessions.$inferSelect;
export type TNewSession = typeof sessions.$inferInsert;

export type TVerificationToken = typeof verificationTokens.$inferSelect;
export type TNewVerificationToken = typeof verificationTokens.$inferInsert;

export type TTeam = typeof teams.$inferSelect;
export type TNewTeam = typeof teams.$inferInsert;

export type TTeamMember = typeof teamMembers.$inferSelect;
export type TNewTeamMember = typeof teamMembers.$inferInsert;

export type TBillingSubscription = typeof billingSubscriptions.$inferSelect;
export type TNewBillingSubscription = typeof billingSubscriptions.$inferInsert;

export type TBillingTransaction = typeof billingTransactions.$inferSelect;
export type TNewBillingTransaction = typeof billingTransactions.$inferInsert;

export type TPaymentMethod = typeof paymentMethods.$inferSelect;
export type TNewPaymentMethod = typeof paymentMethods.$inferInsert;

export type TAdditionalApiRequest = typeof additionalApiRequests.$inferSelect;
export type TNewAdditionalApiRequest = typeof additionalApiRequests.$inferInsert;

export type TProject = typeof projects.$inferSelect;
export type TNewProject = typeof projects.$inferInsert;

export type TCollection = typeof collections.$inferSelect;
export type TNewCollection = typeof collections.$inferInsert;

export type TDocument = typeof documents.$inferSelect;
export type TNewDocument = typeof documents.$inferInsert;

export type TDocumentChunk = typeof documentChunks.$inferSelect;
export type TNewDocumentChunk = typeof documentChunks.$inferInsert;

export type TApiKey = typeof apiKeys.$inferSelect;
export type TNewApiKey = typeof apiKeys.$inferInsert;

export type TAuditLog = typeof auditLogs.$inferSelect;
export type TNewAuditLog = typeof auditLogs.$inferInsert;

export type TEmailNotificationPreferences = typeof emailNotificationPreferences.$inferSelect;
export type TNewEmailNotificationPreferences = typeof emailNotificationPreferences.$inferInsert;

export type TNotificationQueue = typeof notificationQueue.$inferSelect;
export type TNewNotificationQueue = typeof notificationQueue.$inferInsert;

export type TApiKeyRotationPolicy = typeof apiKeyRotationPolicies.$inferSelect;
export type TNewApiKeyRotationPolicy = typeof apiKeyRotationPolicies.$inferInsert;

export type TApiKeyRotationHistory = typeof apiKeyRotationHistory.$inferSelect;
export type TNewApiKeyRotationHistory = typeof apiKeyRotationHistory.$inferInsert;

export type TScheduledApiKeyRotation = typeof scheduledApiKeyRotations.$inferSelect;
export type TNewScheduledApiKeyRotation = typeof scheduledApiKeyRotations.$inferInsert;

/**
 * Better Auth Configuration
 *
 * Authentication and authorization setup for Cloudflare Workers.
 * Configured with PostgreSQL database and session management.
 */

// Lazy import to avoid global scope async operations
// import * as schema from '@dbSchema';
import { neon } from '@neondatabase/serverless';
// Lazy import to avoid global scope async operations
// import { emailService } from '@services/emailService';
import { getEnv } from '@utils/env';
import { betterAuth } from 'better-auth';
import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { drizzle } from 'drizzle-orm/neon-http';

/**
 * Get Better Auth configuration (async version)
 *
 * @returns Better Auth configuration object
 */
const getAuthConfig = async () => {
  const env = await getEnv();

  if (!env.DATABASE_URL) {
    throw new Error('DATABASE_URL environment variable is required');
  }

  if (!env.BETTER_AUTH_SECRET) {
    throw new Error('BETTER_AUTH_SECRET environment variable is required');
  }

  // Dynamic import to avoid global scope async operations
  const schema = await import('@dbSchema');

  // Create Neon HTTP client for Better Auth
  const sql = neon(env.DATABASE_URL);
  const db = drizzle(sql, { schema });
  const database = drizzleAdapter(db, {
    provider: 'pg',
    usePlural: true,
    schema: {
      users: schema.users,
      sessions: schema.sessions,
      accounts: schema.accounts,
      verifications: schema.verificationTokens,
      rateLimits: schema.rateLimit,
    }
  });

  return {
    database,
    secret: env.BETTER_AUTH_SECRET,
    baseURL: env.BETTER_AUTH_URL || 'http://localhost:8787',
  };
};

// Removed getAuthConfigSync to avoid global scope async operations
// The sync version would still require schema import which causes the issue

/**
 * Initialize Better Auth instance
 *
 * Configured for Cloudflare Workers environment with:
 * - PostgreSQL database via Neon
 * - Session management with database storage
 * - Email/password authentication
 * - Secure cookie configuration
 * - Rate limiting and security features
 */
export const createAuth = async () => {
  const config = await getAuthConfig();

  return betterAuth({
    // Database configuration
    database: config.database,

    // Secret for signing cookies and tokens
    secret: config.secret,

    // Base URL for redirects and callbacks
    baseURL: config.baseURL,

    // Email and password authentication
    emailAndPassword: {
      enabled: true,
      requireEmailVerification: false, // Disable for development
      sendResetPassword: async ({ user, url }) => {
        try {
          const { emailService } = await import('@services/emailService');
          await emailService.sendPasswordReset(user.email, user.name || 'User', url);
          console.log(`Password reset email sent to ${user.email}`);
        } catch (error) {
          console.error('Failed to send password reset email:', error);
          // Don't throw error to avoid breaking the auth flow
        }
      },
    },

    // Session configuration
    session: {
      expiresIn: 60 * 60 * 24 * 7, // 7 days
      updateAge: 60 * 60 * 24, // 1 day
      cookieCache: {
        enabled: true,
        maxAge: 5 * 60, // 5 minutes
      },
    },

    // User configuration
    user: {
      additionalFields: {
        // Add custom fields if needed
        role: {
          type: 'string',
          defaultValue: 'user',
        },
      },
    },

    // Advanced security settings
    advanced: {
      // Use secure cookies in production
      useSecureCookies: true,

      // Cookie configuration
      defaultCookieAttributes: {
        httpOnly: true,
        secure: true,
        sameSite: 'lax',
      },

      // IP address tracking for security
      ipAddress: {
        ipAddressHeaders: ['CF-Connecting-IP', 'X-Forwarded-For'],
        disableIpTracking: false,
      },

      // Database configuration
      database: {
        useNumberId: false, // Use UUIDs
        generateId: false, // Let database generate UUIDs
      },
    },

    // Rate limiting
    rateLimit: {
      enabled: true,
      window: 60, // 1 minute
      max: 10, // 10 requests per minute
      storage: 'database',
    },

    // Trusted origins for CORS
    trustedOrigins: [
      'http://localhost:3000',
      'https://localhost:3000',
      'http://localhost:8787',
      'https://localhost:8787',
    ],

    // Error handling
    onAPIError: {
      throw: false,
      onError: (error) => {
        console.error('Better Auth API Error:', {
          error: error instanceof Error ? error.message : String(error),
          timestamp: new Date().toISOString(),
        });
      },
    },
  });
};

/**
 * Global auth instance cache (per-request)
 */
const authInstanceCache = new Map<string, Awaited<ReturnType<typeof createAuth>>>();

/**
 * Get or create auth instance for the current request
 *
 * Uses request-scoped caching to avoid recreating auth instances
 * while ensuring no global scope async operations.
 */
export const getAuth = async () => {
  try {
    // Get a unique key for this request context
    const env = await getEnv();
    const cacheKey = env.DATABASE_URL + env.BETTER_AUTH_SECRET;

    // Check if we have a cached instance for this configuration
    const cachedInstance = authInstanceCache.get(cacheKey);
    if (cachedInstance) {
      return cachedInstance;
    }

    // Create new instance and cache it
    const authInstance = await createAuth();
    authInstanceCache.set(cacheKey, authInstance);

    // Clean up old cache entries to prevent memory leaks
    if (authInstanceCache.size > 10) {
      const firstKey = authInstanceCache.keys().next().value;
      if (firstKey) {
        authInstanceCache.delete(firstKey);
      }
    }

    return authInstance;
  } catch (error) {
    console.error('Failed to create auth instance:', error);
    throw new Error('Authentication service unavailable');
  }
};

/**
 * Type definitions
 */
export type TAuth = Awaited<ReturnType<typeof createAuth>>;

// These will be properly inferred when using the auth instance
export interface ISession {
  user: IUser;
  session: {
    id: string;
    userId: string;
    expiresAt: Date;
    token: string;
    ipAddress?: string;
    userAgent?: string;
    createdAt: Date;
    updatedAt: Date;
  };
}

export interface IUser {
  id: string;
  name: string;
  email: string;
  emailVerified: boolean;
  image?: string;
  role?: string;
  createdAt: Date;
  updatedAt: Date;
}

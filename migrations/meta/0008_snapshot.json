{"id": "a1668108-e4f4-4968-a349-2ae024913ee3", "prevId": "dd9dca58-d9e6-4802-ac2b-fda732b3cd79", "version": "7", "dialect": "postgresql", "tables": {"public.accounts": {"name": "accounts", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "account_id": {"name": "account_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "provider_id": {"name": "provider_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "access_token": {"name": "access_token", "type": "text", "primaryKey": false, "notNull": false}, "refresh_token": {"name": "refresh_token", "type": "text", "primaryKey": false, "notNull": false}, "id_token": {"name": "id_token", "type": "text", "primaryKey": false, "notNull": false}, "access_token_expires_at": {"name": "access_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "refresh_token_expires_at": {"name": "refresh_token_expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "scope": {"name": "scope", "type": "text", "primaryKey": false, "notNull": false}, "password": {"name": "password", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"accounts_user_id_users_id_fk": {"name": "accounts_user_id_users_id_fk", "tableFrom": "accounts", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.additional_api_requests": {"name": "additional_api_requests", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "uuid", "primaryKey": false, "notNull": true}, "request_count": {"name": "request_count", "type": "integer", "primaryKey": false, "notNull": true}, "used_count": {"name": "used_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"additional_api_requests_team_id_teams_id_fk": {"name": "additional_api_requests_team_id_teams_id_fk", "tableFrom": "additional_api_requests", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "additional_api_requests_transaction_id_billing_transactions_id_fk": {"name": "additional_api_requests_transaction_id_billing_transactions_id_fk", "tableFrom": "additional_api_requests", "tableTo": "billing_transactions", "columnsFrom": ["transaction_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_key_rotation_history": {"name": "api_key_rotation_history", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "old_key_id": {"name": "old_key_id", "type": "uuid", "primaryKey": false, "notNull": true}, "new_key_id": {"name": "new_key_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "rotation_type": {"name": "rotation_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'manual'"}, "reason": {"name": "reason", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "old_key_name": {"name": "old_key_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "old_key_prefix": {"name": "old_key_prefix", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "new_key_prefix": {"name": "new_key_prefix", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "rotated_at": {"name": "rotated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "rotated_by": {"name": "rotated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"api_key_rotation_history_new_key_id_api_keys_id_fk": {"name": "api_key_rotation_history_new_key_id_api_keys_id_fk", "tableFrom": "api_key_rotation_history", "tableTo": "api_keys", "columnsFrom": ["new_key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "api_key_rotation_history_user_id_users_id_fk": {"name": "api_key_rotation_history_user_id_users_id_fk", "tableFrom": "api_key_rotation_history", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "api_key_rotation_history_team_id_teams_id_fk": {"name": "api_key_rotation_history_team_id_teams_id_fk", "tableFrom": "api_key_rotation_history", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "api_key_rotation_history_rotated_by_users_id_fk": {"name": "api_key_rotation_history_rotated_by_users_id_fk", "tableFrom": "api_key_rotation_history", "tableTo": "users", "columnsFrom": ["rotated_by"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_key_rotation_policies": {"name": "api_key_rotation_policies", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "enabled": {"name": "enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "rotation_interval_days": {"name": "rotation_interval_days", "type": "integer", "primaryKey": false, "notNull": true}, "rotate_before_expiration_days": {"name": "rotate_before_expiration_days", "type": "integer", "primaryKey": false, "notNull": false}, "overlap_period_hours": {"name": "overlap_period_hours", "type": "integer", "primaryKey": false, "notNull": true, "default": 24}, "notify_before_rotation": {"name": "notify_before_rotation", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "notification_days": {"name": "notification_days", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "key_name_pattern": {"name": "key_name_pattern", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "last_applied_at": {"name": "last_applied_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"api_key_rotation_policies_user_id_users_id_fk": {"name": "api_key_rotation_policies_user_id_users_id_fk", "tableFrom": "api_key_rotation_policies", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "api_key_rotation_policies_team_id_teams_id_fk": {"name": "api_key_rotation_policies_team_id_teams_id_fk", "tableFrom": "api_key_rotation_policies", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.api_keys": {"name": "api_keys", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "key_hash": {"name": "key_hash", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "key_prefix": {"name": "key_prefix", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false}, "scoped_permissions": {"name": "scoped_permissions", "type": "text", "primaryKey": false, "notNull": false}, "usage_limit": {"name": "usage_limit", "type": "integer", "primaryKey": false, "notNull": false}, "usage_count": {"name": "usage_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_used_at": {"name": "last_used_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"api_keys_user_id_users_id_fk": {"name": "api_keys_user_id_users_id_fk", "tableFrom": "api_keys", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "api_keys_team_id_teams_id_fk": {"name": "api_keys_team_id_teams_id_fk", "tableFrom": "api_keys", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.audit_logs": {"name": "audit_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource": {"name": "resource", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "resource_id": {"name": "resource_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"audit_logs_user_id_users_id_fk": {"name": "audit_logs_user_id_users_id_fk", "tableFrom": "audit_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}, "audit_logs_team_id_teams_id_fk": {"name": "audit_logs_team_id_teams_id_fk", "tableFrom": "audit_logs", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "set null", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_subscriptions": {"name": "billing_subscriptions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "plan_id": {"name": "plan_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "current_period_start": {"name": "current_period_start", "type": "timestamp", "primaryKey": false, "notNull": true}, "current_period_end": {"name": "current_period_end", "type": "timestamp", "primaryKey": false, "notNull": true}, "cancel_at_period_end": {"name": "cancel_at_period_end", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_subscriptions_team_id_teams_id_fk": {"name": "billing_subscriptions_team_id_teams_id_fk", "tableFrom": "billing_subscriptions", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.billing_transactions": {"name": "billing_transactions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "amount": {"name": "amount", "type": "integer", "primaryKey": false, "notNull": true}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(3)", "primaryKey": false, "notNull": true, "default": "'USD'"}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'subscription'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"billing_transactions_team_id_teams_id_fk": {"name": "billing_transactions_team_id_teams_id_fk", "tableFrom": "billing_transactions", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"billing_transactions_transaction_id_unique": {"name": "billing_transactions_transaction_id_unique", "nullsNotDistinct": false, "columns": ["transaction_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.collections": {"name": "collections", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "project_id": {"name": "project_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'docs'"}, "embedding_provider": {"name": "embedding_provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'cloudflare'"}, "embedding_model": {"name": "embedding_model", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true, "default": "'@cf/baai/bge-large-en-v1.5'"}, "vector_dimensions": {"name": "vector_dimensions", "type": "integer", "primaryKey": false, "notNull": true, "default": 1024}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"collections_project_id_projects_id_fk": {"name": "collections_project_id_projects_id_fk", "tableFrom": "collections", "tableTo": "projects", "columnsFrom": ["project_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "collections_team_id_teams_id_fk": {"name": "collections_team_id_teams_id_fk", "tableFrom": "collections", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.document_chunks": {"name": "document_chunks", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "document_id": {"name": "document_id", "type": "uuid", "primaryKey": false, "notNull": true}, "vector_id": {"name": "vector_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "chunk_index": {"name": "chunk_index", "type": "integer", "primaryKey": false, "notNull": true}, "content": {"name": "content", "type": "text", "primaryKey": false, "notNull": true}, "content_hash": {"name": "content_hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "token_count": {"name": "token_count", "type": "integer", "primaryKey": false, "notNull": false}, "metadata": {"name": "metadata", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"document_chunks_document_id_documents_id_fk": {"name": "document_chunks_document_id_documents_id_fk", "tableFrom": "document_chunks", "tableTo": "documents", "columnsFrom": ["document_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.documents": {"name": "documents", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "collection_id": {"name": "collection_id", "type": "uuid", "primaryKey": false, "notNull": true}, "filename": {"name": "filename", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "original_name": {"name": "original_name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "mime_type": {"name": "mime_type", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": true}, "content_hash": {"name": "content_hash", "type": "<PERSON><PERSON><PERSON>(64)", "primaryKey": false, "notNull": true}, "r2_key": {"name": "r2_key", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": true}, "processing_status": {"name": "processing_status", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "error_message": {"name": "error_message", "type": "text", "primaryKey": false, "notNull": false}, "chunk_count": {"name": "chunk_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"documents_collection_id_collections_id_fk": {"name": "documents_collection_id_collections_id_fk", "tableFrom": "documents", "tableTo": "collections", "columnsFrom": ["collection_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.email_notification_preferences": {"name": "email_notification_preferences", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "expiration_warnings": {"name": "expiration_warnings", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "expiration_warning_days": {"name": "expiration_warning_days", "type": "text", "primaryKey": false, "notNull": true, "default": "'[7,1]'"}, "rotation_notifications": {"name": "rotation_notifications", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "rotation_failure_alerts": {"name": "rotation_failure_alerts", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "bulk_operation_summaries": {"name": "bulk_operation_summaries", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "team_expiration_summaries": {"name": "team_expiration_summaries", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "email_enabled": {"name": "email_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "webhook_enabled": {"name": "webhook_enabled", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "webhook_url": {"name": "webhook_url", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "max_emails_per_day": {"name": "max_emails_per_day", "type": "integer", "primaryKey": false, "notNull": true, "default": 10}, "quiet_hours_start": {"name": "quiet_hours_start", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "quiet_hours_end": {"name": "quiet_hours_end", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "quiet_hours_timezone": {"name": "quiet_hours_timezone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'UTC'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"email_notification_preferences_user_id_users_id_fk": {"name": "email_notification_preferences_user_id_users_id_fk", "tableFrom": "email_notification_preferences", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "email_notification_preferences_team_id_teams_id_fk": {"name": "email_notification_preferences_team_id_teams_id_fk", "tableFrom": "email_notification_preferences", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.notification_queue": {"name": "notification_queue", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "priority": {"name": "priority", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true, "default": "'normal'"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_email": {"name": "user_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "context": {"name": "context", "type": "text", "primaryKey": false, "notNull": true}, "scheduled_at": {"name": "scheduled_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "max_retries": {"name": "max_retries", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "retry_count": {"name": "retry_count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "last_attempt_at": {"name": "last_attempt_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "sent_at": {"name": "sent_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"notification_queue_user_id_users_id_fk": {"name": "notification_queue_user_id_users_id_fk", "tableFrom": "notification_queue", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "notification_queue_team_id_teams_id_fk": {"name": "notification_queue_team_id_teams_id_fk", "tableFrom": "notification_queue", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.payment_methods": {"name": "payment_methods", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "provider": {"name": "provider", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "provider_payment_method_id": {"name": "provider_payment_method_id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "card_last4": {"name": "card_last4", "type": "<PERSON><PERSON><PERSON>(4)", "primaryKey": false, "notNull": false}, "card_brand": {"name": "card_brand", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "card_expiry_month": {"name": "card_expiry_month", "type": "integer", "primaryKey": false, "notNull": false}, "card_expiry_year": {"name": "card_expiry_year", "type": "integer", "primaryKey": false, "notNull": false}, "paypal_email": {"name": "paypal_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "is_default": {"name": "is_default", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"payment_methods_team_id_teams_id_fk": {"name": "payment_methods_team_id_teams_id_fk", "tableFrom": "payment_methods", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.projects": {"name": "projects", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_public": {"name": "is_public", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"projects_user_id_users_id_fk": {"name": "projects_user_id_users_id_fk", "tableFrom": "projects", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "projects_team_id_teams_id_fk": {"name": "projects_team_id_teams_id_fk", "tableFrom": "projects", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.rate_limit": {"name": "rate_limit", "schema": "", "columns": {"id": {"name": "id", "type": "text", "primaryKey": true, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "count": {"name": "count", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "last_request": {"name": "last_request", "type": "bigint", "primaryKey": false, "notNull": true}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"rate_limit_key_unique": {"name": "rate_limit_key_unique", "nullsNotDistinct": false, "columns": ["key"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.scheduled_api_key_rotations": {"name": "scheduled_api_key_rotations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "key_id": {"name": "key_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": false}, "scheduled_at": {"name": "scheduled_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "rotation_type": {"name": "rotation_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "reason": {"name": "reason", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "overlap_period_hours": {"name": "overlap_period_hours", "type": "integer", "primaryKey": false, "notNull": true, "default": 24}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "executed_at": {"name": "executed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "failure_reason": {"name": "failure_reason", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"scheduled_api_key_rotations_key_id_api_keys_id_fk": {"name": "scheduled_api_key_rotations_key_id_api_keys_id_fk", "tableFrom": "scheduled_api_key_rotations", "tableTo": "api_keys", "columnsFrom": ["key_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "scheduled_api_key_rotations_user_id_users_id_fk": {"name": "scheduled_api_key_rotations_user_id_users_id_fk", "tableFrom": "scheduled_api_key_rotations", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "scheduled_api_key_rotations_team_id_teams_id_fk": {"name": "scheduled_api_key_rotations_team_id_teams_id_fk", "tableFrom": "scheduled_api_key_rotations", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.sessions": {"name": "sessions", "schema": "", "columns": {"id": {"name": "id", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": true, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "expires_at": {"name": "expires_at", "type": "timestamp", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"sessions_user_id_users_id_fk": {"name": "sessions_user_id_users_id_fk", "tableFrom": "sessions", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"sessions_token_unique": {"name": "sessions_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.team_members": {"name": "team_members", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "team_id": {"name": "team_id", "type": "uuid", "primaryKey": false, "notNull": true}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "role": {"name": "role", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'member'"}, "permissions": {"name": "permissions", "type": "text", "primaryKey": false, "notNull": false}, "invited_by": {"name": "invited_by", "type": "uuid", "primaryKey": false, "notNull": false}, "joined_at": {"name": "joined_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"team_members_team_id_teams_id_fk": {"name": "team_members_team_id_teams_id_fk", "tableFrom": "team_members", "tableTo": "teams", "columnsFrom": ["team_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "team_members_user_id_users_id_fk": {"name": "team_members_user_id_users_id_fk", "tableFrom": "team_members", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}, "team_members_invited_by_users_id_fk": {"name": "team_members_invited_by_users_id_fk", "tableFrom": "team_members", "tableTo": "users", "columnsFrom": ["invited_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teams": {"name": "teams", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "slug": {"name": "slug", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "owner_id": {"name": "owner_id", "type": "uuid", "primaryKey": false, "notNull": true}, "plan_type": {"name": "plan_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true, "default": "'free'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "deleted_at": {"name": "deleted_at", "type": "timestamp", "primaryKey": false, "notNull": false}}, "indexes": {}, "foreignKeys": {"teams_owner_id_users_id_fk": {"name": "teams_owner_id_users_id_fk", "tableFrom": "teams", "tableTo": "users", "columnsFrom": ["owner_id"], "columnsTo": ["id"], "onDelete": "cascade", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"teams_slug_unique": {"name": "teams_slug_unique", "nullsNotDistinct": false, "columns": ["slug"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "email_verified": {"name": "email_verified", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "image": {"name": "image", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.verification_tokens": {"name": "verification_tokens", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "identifier": {"name": "identifier", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "token": {"name": "token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "expires": {"name": "expires", "type": "timestamp", "primaryKey": false, "notNull": true}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"verification_tokens_token_unique": {"name": "verification_tokens_token_unique", "nullsNotDistinct": false, "columns": ["token"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}